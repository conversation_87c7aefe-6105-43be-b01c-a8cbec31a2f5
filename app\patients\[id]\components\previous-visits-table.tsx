"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Calendar, User, Camera, Clock, Download, Plus, Check, X, Upload } from "lucide-react"
import { FMS_LAYOUT, PROCEDURE_COLORS, XrayImage, Annotation, Prediction, AnalysisResult } from "../types"
import { useDispatch } from "react-redux"
import { addPatientVisitId, addPreviousPatientsVisits, addPreviousVisitLoadedImagesCompleteData, addPreviousVisitLoadedXraysList, clearPreviousVisitLoadedImagesCompleteData, clearPreviousVisitLoadedXraysList, removePatientVisitId, resetPreviousVisitClicked, togglePreviousVisitClicked, updateVisitImageCount } from "@/store/previousVisits"
import Cookies from "js-cookie";
import { useSelector } from "react-redux"
import { RootState } from "@/store/store"
import { RxReload } from "react-icons/rx";
import { useRouter } from "next/navigation"
import fetchWithRefresh from "@/constants/useRefreshAccessToken"
import { setPanoramicImage, setSlots, setUploading, setAnnotations } from "@/store/fullMouthSeriesSlice"
import { useAppSelector } from "@/store/hooks"
import { selectVisitId } from "@/store/visitSlice"
import { IMAGE_ANALYSIS } from "@/constants/apiRoutes"

interface Procedure {
  code: string
  name: string
  toothNumber?: string
}

interface Visit {
  visitId: string
  visitDate: string
  dentistName: string
  createdAt: string
  procedures: Procedure[]
  imageCount: number
}

interface PreviousVisitsTableProps {
  patientId: string
  onVisitSelect: (visit: Visit) => void
  onVisitCreate?: (visit: Omit<Visit, 'visitId'>) => void
  selectedVisitId?: string
  onImagesLoad?: (imageLinks: string[]) => void
  onBulkUpload: (files: FileList | null, visitId?: string) => Promise<any>;
  currentid: any;
  setcurrentid: any;
  
}

interface NewVisit {
  date: string
  dentistName: string
  imageCount: number
  procedures: Procedure[]
  createdAt: string
}

const API_BASE_URL = "https://jedaiportal-714292203960.us-east1.run.app//api"

export function PreviousVisitsTable({
  patientId,
  onVisitSelect,
  onVisitCreate,
  selectedVisitId,
  onImagesLoad,
  onBulkUpload,
  currentid,
  setcurrentid
}: PreviousVisitsTableProps) {
  const dispatch = useDispatch()
  const router = useRouter()
  const visits = useSelector((state: RootState) => state.previousVisit.previousPatientVisits);
  const [highlightedRowId, setHighlightedRowId] = useState<string | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const displayName = useSelector((state: RootState) => state.user.displayName);
  const [newVisit, setNewVisit] = useState<NewVisit>({
    date: new Date().toISOString().split('T')[0],
    dentistName: '',
    imageCount: 0,
    procedures: [],
    createdAt: new Date().toISOString()
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const visitId = useAppSelector(selectVisitId);
  const [loadingImages, setLoadingImages] = useState<{ [key: string]: boolean }>({})
  const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  const analyzing = useSelector((state: RootState) => state.fullMouthSeries.analyzing);
  const uploading = useSelector((state: RootState) => state.fullMouthSeries.uploading);
  const dentistName = useSelector((state: RootState) => state.user.displayName)

  const fetchVisits = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/Treatment/getAllPatientVisits/${patientId}`,
        { method: "GET", credentials: "include" },
        router
      );

      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to fetch visits");
      }

      const data = await response.json();
      const raw = Array.isArray(data.data) ? data.data : Array.isArray(data) ? data : [];
      
      const formattedVisits = raw.map((visit: any) => ({
        visitId: visit.visitId.toString(),
        visitDate: new Date(visit.visitDate).toLocaleDateString('en-US'),
        dentistName: displayName || visit.dentistName,
        createdAt: new Date(visit.createdAt).toLocaleString(),
        procedures: visit.procedures || [],
        imageCount: visit.imageCount || 0,
      }));

      dispatch(addPreviousPatientsVisits(formattedVisits));
    } catch (err: any) {
      setError(err.message || "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (patientId) {
      fetchVisits();
    }
  }, [patientId, router, dispatch]);

  const handleBulkUpload = async (files: FileList | null) => {
    if (!files) return;

    try {
      const newUploading: { [key: string]: boolean } = { ...uploading, PANORAMIC: false };
      const fileArray = Array.from(files);
      fileArray.forEach((file, index) => {
        const slotId = fileArray.length === 1 ? "PANORAMIC" : FMS_LAYOUT[index % FMS_LAYOUT.length].id;
        newUploading[slotId] = true;
      });
      dispatch(setUploading(newUploading));

      const result = await onBulkUpload(files, visitId?.toString());
      fetchVisits();
      dispatch(updateVisitImageCount({
        visitId: visitId!,
        newCount: result.total
      }));

      if (result?.uploads) {
        const newSlots: Record<string, XrayImage | null> = {};
        let newPanoramicImage: XrayImage | null = null;

        result.uploads.forEach((upload: any) => {
          if (upload.imageType === "PANORAMIC") {
            newPanoramicImage = {
              id: upload.id.toString(),
              imageId: upload.id,
              url: upload.url,
              type: "PANORAMIC",
              position: "panoramic",
              date: new Date().toISOString(),
              analyzed: false,
              findings: [],
            };
          } else {
            const slot = FMS_LAYOUT.find(s => s.id.toLowerCase() === upload.imageType);
            if (slot) {
              newSlots[slot.id] = {
                id: upload.id.toString(),
                imageId: upload.id,
                url: upload.url,
                type: slot.type,
                position: slot.position,
                date: new Date().toISOString(),
                analyzed: false,
                findings: [],
              };
            }
          }
        });

        dispatch(setSlots({ ...slots, ...newSlots }));
        if (newPanoramicImage) {
          dispatch(setPanoramicImage(newPanoramicImage));
        }
      }
    } catch (error) {
      console.error('Error during bulk upload:', error);
    } finally {
      dispatch(setUploading({ ...uploading, PANORAMIC: false, ...Object.fromEntries(FMS_LAYOUT.map(slot => [slot.id, false])) }));
    }
  };

  const newVisitRow: Visit = {
    visitId: 'new',
    visitDate: new Date().toLocaleDateString('en-US'),
    dentistName: displayName || "Dentist",
    createdAt: new Date().toLocaleString(),
    procedures: [],
    imageCount: 0
  };

  const handleRowClick = (visit: Visit) => {
    setHighlightedRowId(visit.visitId);

    if (visit.visitId === 'new') {
      dispatch(clearPreviousVisitLoadedImagesCompleteData());
      dispatch(clearPreviousVisitLoadedXraysList());
      dispatch(resetPreviousVisitClicked());
    }

    onVisitSelect(visit);
  };

 const handleLoadImages = async (visit: Visit, e: React.MouseEvent) => {
    e.stopPropagation();
    setLoadingImages(prev => ({ ...prev, [visit.visitId]: true }));
    setError(null);
    setcurrentid(visit);

    try {
      // Fetch images
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/ImageUploader/image-links/${patientId}/${visit.visitId}/visitDate?visitDate=${(visit.visitDate)}`,
        { method: "GET", credentials: "include" },
        router
      );

      if (!response) throw new Error("Session expired");
      if (!response.ok) throw new Error("Failed to fetch images");
      
      const data = await response.json();
      console.log("Image data:", data);

      // Fetch analysis data
      let analysisData: { results: AnalysisResult[] } = { results: [] };
      try {
        const analysisResponse = await fetchWithRefresh(
          `${IMAGE_ANALYSIS}/${patientId}/${visit.visitId}?visitDate=${(visit.visitDate)}`,
          { method: "GET", credentials: "include" },
          router
        );
        
        if (analysisResponse && analysisResponse.ok) {
          const rawAnalysisData = await analysisResponse.json();
          if (rawAnalysisData && Array.isArray(rawAnalysisData.results)) {
            analysisData = rawAnalysisData;
          } else {
            console.warn("Analysis data from API did not match expected format:", rawAnalysisData);
          }
          console.log("Analysis data:", analysisData);
        }
      } catch (analysisError) {
        console.warn("Could not load analysis data:", analysisError);
      }

      // Combine data
      const xrays = data.results.map((result: any) => {
        const analysis = analysisData.results.find((a) => 
          a.imageId === result.id || 
          a.imageUrl === result.url ||
          a.imageId?.toString() === result.id?.toString()
        );

        return {
          id: result.url.split("/").pop()?.split(".")[0] || `${Date.now()}`,
          imageId: result.id,
          url: result.url,
          type: result.type,
          date: new Date().toISOString(),
          analyzed: !!analysis && analysis.analysis && analysis.analysis.length > 0,
          findings: [], // Findings are now managed by Redux annotations state
        };
      });

      // Process analysis data into the Redux-expected format (Record<string, Annotation>)
      const allAnnotationsForVisit: Record<string, Annotation> = {};
      analysisData.results.forEach((analysisResult: any) => {
        const imageType = analysisResult.imageType.toUpperCase(); // e.g., "LB1", "PANORAMIC"
        // const imageId = analysisResult.imageId; // Not directly used here, but good for context

        let numbering: Prediction[] = [];
        let decay: Prediction[] = [];

        (analysisResult.analysis || []).forEach((annotationGroup: any) => {
          if (annotationGroup.annotationType === "TeethNumbering" && annotationGroup.annotations) {
            annotationGroup.annotations.forEach((ann: any) => {
              try {
                const parsedAnnotations = JSON.parse(ann.annotationJson);
                (Array.isArray(parsedAnnotations) ? parsedAnnotations : [parsedAnnotations]).forEach((pred: any) => {
                  numbering.push({
                    conf_score: pred.conf_score,
                    category_id: pred.category_id,
                    segmentation: pred.segmentation,
                    annotation_source: ann.annotationSource,
                    category_name: pred.category_name // Ensure category_name is included if available
                  });
                });
              } catch (e) {
                console.error("Failed to parse numbering annotationJson:", e);
              }
            });
          } else if (annotationGroup.annotationType === "TeethDecay" && annotationGroup.annotations) {
            annotationGroup.annotations.forEach((ann: any) => {
              try {
                const parsedAnnotations = JSON.parse(ann.annotationJson);
                (Array.isArray(parsedAnnotations) ? parsedAnnotations : [parsedAnnotations]).forEach((pred: any) => {
                  decay.push({
                    conf_score: pred.conf_score,
                    category_id: pred.category_id,
                    segmentation: pred.segmentation,
                    annotation_source: ann.annotationSource,
                    category_name: pred.category_name // Ensure category_name is included if available
                  });
                });
              } catch (e) {
                console.error("Failed to parse decay annotationJson:", e);
              }
            });
          }
        });

        // Store annotations by imageType (slotId)
        allAnnotationsForVisit[imageType] = { numbering, decay };
      });

      // Dispatch all processed annotations to Redux
      dispatch(setAnnotations(allAnnotationsForVisit)); // This is the key to display annotations

      // Add timeout to ensure annotations have time to process/display
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

      dispatch(addPreviousVisitLoadedImagesCompleteData(data));
      dispatch(addPatientVisitId(parseInt(visit.visitId, 10)));
      dispatch(addPreviousVisitLoadedXraysList(xrays));
      dispatch(togglePreviousVisitClicked());

      if (onImagesLoad) {
        onImagesLoad(xrays);
      }

      const updatedVisit = {
        ...visit,
        xrays,
        imageCount: data.total,
      };
      onVisitSelect(updatedVisit);
      
    } catch (err: any) {
      console.error("Error loading images:", err);
      setError(err.message || "Failed to load images");
    } finally {
      setLoadingImages(prev => ({ ...prev, [visit.visitId]: false }));
    }
  };

  const today = new Date().toLocaleDateString('en-US')
  const hasTodayVisit = visits?.some(v => v.visitDate === today) ?? false

  if (loading) {
  return (
    <Card className="w-full bg-card border-border">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl text-foreground">Previous Visits & X-rays</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-center py-8">
          <div className="text-muted-foreground">Loading visits...</div>
        </div>
      </CardContent>
    </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full bg-card border-border">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl text-foreground">Previous Visits & X-rays</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8 text-destructive">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full bg-card border-border">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl text-foreground">Visits</CardTitle>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-12 gap-4 pb-3 border-b border-border text-sm font-medium text-muted-foreground">
          <div className="col-span-2 flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Visit Date
          </div>
          <div className="col-span-2 flex items-center gap-2">
            <User className="w-4 h-4" />
            Dentist
          </div>
          <div className="col-span-2 flex items-center gap-2">
            <Camera className="w-4 h-4" />
            X-rays Taken
          </div>
          <div className="col-span-2">Procedures</div>
          <div className="col-span-2 flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Created At
          </div>
          <div className="col-span-2 text-center">Actions</div>
        </div>

        <div className="space-y-2 mt-4 max-h-[500px] overflow-y-auto">
          {!hasTodayVisit && (
            <div
              onClick={() => handleRowClick(newVisitRow)}
              className={`grid grid-cols-12 gap-4 p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md bg-accent/20 border-primary/30`}
            >
              <div className="col-span-2 font-medium text-foreground flex items-center">
                {newVisitRow.visitDate}
              </div>

              <div className="col-span-2 text-muted-foreground flex items-center">
                {newVisitRow.dentistName}
              </div>

              <div className="col-span-2 font-medium text-foreground flex items-center">
                {/* {newVisitRow.imageCount} X-rays */}
              </div>

              <div className="col-span-2 space-y-1">
                <span className="text-sm text-muted-foreground">No procedures</span>
              </div>

              <div className="col-span-2 text-sm text-muted-foreground flex items-center">
                {/* {newVisitRow.createdAt} */}
              </div>

              <div className="col-span-2 flex justify-center">
                <label htmlFor="bulk-upload-input" className="cursor-pointer">
<Button
  variant="outline"
  size="sm"
  className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 flex items-center gap-1"
  onClick={() => {
  document.getElementById("bulk-upload-input")?.click();
  }}
  >
  <Upload className="w-4 h-4 mr-2" />
      Bulk Upload
</Button>
                </label>
                <input
                  id="bulk-upload-input"
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => handleBulkUpload(e.target.files)}
                />
              </div>
            </div>
          )}

          {!visits || visits.length === 0 ? (
            <div className="flex justify-center py-8 text-muted-foreground">
              No previous visits found
            </div>
          ) : (
            visits.map((visit) => (
              <div
                key={visit.visitId}
                onClick={() => handleRowClick(visit)}
                className={`grid grid-cols-12 gap-4 p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${highlightedRowId === visit.visitId
                    ? "bg-primary/10 border-primary/30 ring-2 ring-primary/20"
                    : selectedVisitId === visit.visitId
                      ? "bg-green-500/10 border-green-500/30"
                      : "bg-accent/10 border-border hover:bg-accent/20"
                  }`}
              >
                <div className="col-span-2 font-medium text-foreground flex items-center">
                  {visit.visitDate}
                </div>

                <div className="col-span-2 text-muted-foreground flex items-center">
                  {visit.dentistName}
                </div>

                <div className="col-span-2 font-medium text-foreground flex items-center">
                  {visit.imageCount} X-rays
                </div>

                <div className="col-span-2 space-y-1">
                  {visit.procedures.length > 0 ? (
                    visit.procedures.map((procedure, index) => (
                      <div key={index} className="flex items-center gap-2 flex-wrap">
                        <Badge
                          variant="outline"
                          className={`text-xs px-2 py-1 ${PROCEDURE_COLORS[procedure.code] || "bg-muted text-muted-foreground border-border"
                            }`}
                        >
                          {procedure.code}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {procedure.name}
                          {procedure.toothNumber && <span className="ml-1 font-medium">#{procedure.toothNumber}</span>}
                        </span>
                      </div>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">No procedures</span>
                  )}
                </div>

                <div className="col-span-2 text-sm text-muted-foreground flex items-center">
                  {visit.createdAt}
                </div>

                <div className="col-span-2 flex justify-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => handleLoadImages(visit, e)}
                    className="text-xs bg-primary/10 border-primary/30 text-primary hover:bg-primary/20 px-3 py-1 flex items-center gap-1"
                    disabled={loadingImages[visit.visitId]}
                  >
                    {loadingImages[visit.visitId] ? (
                      <svg
                        className="animate-spin h-3 w-3 text-primary"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    ) : (
                      <>
                        <RxReload className="w-3 h-3" />
                        <span>Load Images</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
