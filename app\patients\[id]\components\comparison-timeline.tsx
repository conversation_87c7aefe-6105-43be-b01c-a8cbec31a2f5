"use client";

import { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { DecayOverlayImage } from "./DecayOverlayImage";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  TrendingUp,
  AlertCircle,
} from "lucide-react";
import { FMS_LAYOUT, Prediction, Visit } from "../types";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useDispatch } from "react-redux";
import { setSlots, setAnnotations } from "@/store/fullMouthSeriesSlice";
import { PREDICTION_URL } from "@/constants/apiRoutes";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { XrayImage } from "@/app/patients/[id]/constants";
import { Badge } from "@/components/ui/badge";

interface Props {
  visits: Visit[];
}

interface RawLink {
  id: number;
  url: string;
  type: string;
}

// adjust these labels to whatever your back-end spec uses
const DECAY_TYPE_LABELS: Record<number, string> = {
  1: "Incipient Decay",
  2: "Moderate Decay",
  3: "Advanced Decay",
  4: "Severe Decay",
};

// right below your DECAY_TYPE_LABELS
const IMAGE_TYPE_LABELS: Record<string, string> = {
  // Upper Left Periapical series
  ULP1: "Upper Left Periapical 1",
  ULP2: "Upper Left Periapical 2",
  // Right Bitewing series
  RB1: "Right Bitewing 1",
  RB2: "Right Bitewing 2",
  // Lower Periapical Right
  LPR1: "Lower Periapical Right 1",
  LPR2: "Lower Periapical Right 2",
  // Lower Bitewing Left
  LB1: "Lower Bitewing 1",
  LB2: "Lower Bitewing 2",
  // …and so on for every code your server returns
};

interface PredictionEntry {
  image_id: number;
  predictions: {
    teeth_numbering: Prediction[];
    teeth_decay: Prediction[];
  };
}

export function ComparisonTimeline({ visits }: Props) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { id: patientId } = useParams();
  const router = useRouter();
  const [allTypes, setAllTypes] = useState<string[]>([]);
  const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  const displayName = useSelector((state: RootState) => state.user.displayName);
  const annotations = useSelector(
    (state: RootState) => state.fullMouthSeries.annotations
  );
  console.log(" slots from store:", slots);
  console.log("🗂️ annotations from store:", annotations);
  const [imageLinksPrev, setImageLinksPrev] = useState<string[]>([]);
  const [imageLinksCurr, setImageLinksCurr] = useState<string[]>([]);
  const [prevDecayMasks, setPrevDecayMasks] = useState<Prediction[][]>([]);
  const [currDecayMasks, setCurrDecayMasks] = useState<Prediction[][]>([]);
  const [imagesLoading, setImagesLoading] = useState(false);
  const [currIdx, setCurrIdx] = useState(0);

  const prevUploadedCount = imageLinksPrev.filter((url) => url).length;
  const currUploadedCount = imageLinksCurr.filter((url) => url).length;

  // after `const [allTypes, setAllTypes] = useState<string[]>([])`
  useEffect(() => {
    // only reset once we actually have masks
    if (allTypes.length > 0) {
      setCurrIdx(0);
    }
  }, [allTypes]);

  console.log("🏥 patientId from store:", patientId);
  useEffect(() => {
    console.log("Prev masks for this idx:", prevDecayMasks[currIdx]);
    console.log("Curr masks for this idx:", currDecayMasks[currIdx]);
  }, [currIdx, prevDecayMasks, currDecayMasks]);

  const fmt = (iso: string) =>
    new Date(iso).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      year: "numeric",
    });

  const fmtDate = (iso: string) =>
    new Date(iso).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      year: "numeric",
    });

  const [selected, setSelected] = useState<{
    previous: Visit;
    current: Visit;
  } | null>(null);

  // pick newest two
  useEffect(() => {
    if (visits.length >= 2 && !selected) {
      console.log("✔️ selecting default visits", visits[0], visits[1]);
      setSelected({ current: visits[0], previous: visits[1] });
    }
  }, [visits, selected]);

  useEffect(() => {
    if (visits.length >= 2 && !selected) {
      setSelected({ current: visits[0], previous: visits[1] });
    }
  }, [visits, selected]);

useEffect(() => {
  if (!selected || !patientId) return;

  const { previous, current } = selected;
  const prevDate = previous.visitDate.slice(0, 10);
  const currDate = current.visitDate.slice(0, 10);

  interface RawLink {
    id: number;
    url: string;
    type: string;
  }

  const postPredictions = async (
    visitId: string,
    payload: { imageId: number; imageType: string }[],
    retries = 2
  ): Promise<PredictionEntry[]> => {
    try {
      const res = await fetchWithRefresh(
        `${PREDICTION_URL}${visitId}`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ images: payload }),
        },
        router
      );
      if (!res || (!res.ok && retries > 0)) {
        if (res && retries > 0) {
          await new Promise((r) => setTimeout(r, 500));
          return postPredictions(visitId, payload, retries - 1);
        }
        throw new Error("Prediction request failed");
      }
      const data = await res.json();
      return Array.isArray(data) ? data : [];
    } catch {
      return [];
    }
  };

  const fetchAll = async () => {
    console.log('[ComparisonTimeline] ⏳ fetchAll start');
    setImagesLoading(true);

    try {
      // 1) fetch image-link lists
      const prevUrl = `https://jedaiportal-gpu-714292203960.us-central1.run.app//api/ImageUploader/image-links/${patientId}/${previous.visitId}/visitDate?visitDate=${prevDate}`;
      const currUrl = `https://jedaiportal-gpu-714292203960.us-central1.run.app//api/ImageUploader/image-links/${patientId}/${current.visitId}/visitDate?visitDate=${currDate}`;
      console.log('[ComparisonTimeline] Fetching prev images from:', prevUrl);
      console.log('[ComparisonTimeline] Fetching curr images from:', currUrl);

      const [prevRes, currRes] = await Promise.all([
        fetchWithRefresh(prevUrl, { credentials: "include" }, router),
        fetchWithRefresh(currUrl, { credentials: "include" }, router),
      ]);

      // 2) guard and log HTTP errors
      if (!prevRes) throw new Error("Failed to fetch previous images");
      if (!currRes) throw new Error("Failed to fetch current images");
      if (!prevRes.ok) {
        console.error('[ComparisonTimeline] prevRes failed:', prevRes.status, await prevRes.text());
      }
      if (!currRes.ok) {
        console.error('[ComparisonTimeline] currRes failed:', currRes.status, await currRes.text());
      }

      // 3) parse JSON
      const prevResults: RawLink[] = (await prevRes.json()).results;
      const currResults: RawLink[] = (await currRes.json()).results;
      console.log('[ComparisonTimeline] prevJson.results:', prevResults);
      console.log('[ComparisonTimeline] currJson.results:', currResults);

      // 4) union & normalize types
      const types = Array.from(new Set([
        ...prevResults.map(r => r.type),
        ...currResults.map(r => r.type),
      ]));
      console.log('[ComparisonTimeline] raw types:', types);
      const normalized = types.map(t =>
        t.split("_").map(w => w.charAt(0).toUpperCase() + w.slice(1)).join("")
      );
      console.log('[ComparisonTimeline] normalized types:', normalized);

      // 5) align into same order
      const prevByType = types.map(t => prevResults.find(r => r.type === t) || null);
      const currByType = types.map(t => currResults.find(r => r.type === t) || null);

      // 6) build payloads
      const prevPayload = prevByType.filter((x): x is RawLink => x !== null)
                                    .map(r => ({ imageId: r.id, imageType: r.type }));
      const currPayload = currByType.filter((x): x is RawLink => x !== null)
                                    .map(r => ({ imageId: r.id, imageType: r.type }));
      console.log('[ComparisonTimeline] prevPayload:', prevPayload);
      console.log('[ComparisonTimeline] currPayload:', currPayload);

      // 7) fetch predictions
      const [prevEntries, currEntries] = await Promise.all([
        postPredictions(previous.visitId, prevPayload),
        postPredictions(current.visitId, currPayload),
      ]);
      console.log('[ComparisonTimeline] prevEntries:', prevEntries);
      console.log('[ComparisonTimeline] currEntries:', currEntries);

      // 8) assemble slots, annotations & parallel arrays
      const newSlots: Record<string, XrayImage> = {};
      const newAnns: Record<string, { decay: Prediction[]; numbering: Prediction[] }> = {};
      const urlsPrev: string[] = [];
      const urlsCurr: string[] = [];
      const masksPrev: Prediction[][] = [];
      const masksCurr: Prediction[][] = [];

      types.forEach((type, i) => {
        const p = prevByType[i];
        const pe = p && prevEntries.find(e => e.image_id === p.id);
        if (p) {
          urlsPrev.push(p.url);
          masksPrev.push(pe?.predictions.teeth_decay ?? []);
          newSlots[type] = {
            id: type, imageId: p.id, url: p.url, type: p.type as any,
            position: "", date: previous.visitDate,
            analyzed: !!pe?.predictions.teeth_decay.length,
            status: "", imageType: type, findings: [],
          };
          newAnns[type] = {
            decay: pe?.predictions.teeth_decay ?? [],
            numbering: pe?.predictions.teeth_numbering ?? [],
          };
        } else {
          urlsPrev.push("");
          masksPrev.push([]);
        }

        const c = currByType[i];
        const ce = c && currEntries.find(e => e.image_id === c.id);
        if (c) {
          urlsCurr.push(c.url);
          masksCurr.push(ce?.predictions.teeth_decay ?? []);
          newSlots[type] = {
            id: type, imageId: c.id, url: c.url, type: c.type as any,
            position: "", date: current.visitDate,
            analyzed: !!ce?.predictions.teeth_decay.length,
            status: "", imageType: type, findings: [],
          };
          newAnns[type] = {
            decay: ce?.predictions.teeth_decay ?? [],
            numbering: ce?.predictions.teeth_numbering ?? [],
          };
        } else {
          urlsCurr.push("");
          masksCurr.push([]);
        }
      });

      console.log('[ComparisonTimeline] urlsPrev:', urlsPrev);
      console.log('[ComparisonTimeline] masksPrev:', masksPrev);
      console.log('[ComparisonTimeline] urlsCurr:', urlsCurr);
      console.log('[ComparisonTimeline] masksCurr:', masksCurr);

      // --- FILTER OUT any index where BOTH prev & curr have no decay ---
      const hasDecay = masksPrev.map((prevMask, i) =>
        prevMask.length > 0 || masksCurr[i].length > 0
      );

      const filteredTypes = types.filter((_, i)   => hasDecay[i]);
      const filteredUrlsPrev  = urlsPrev.filter((_,    i) => hasDecay[i]);
      const filteredMasksPrev = masksPrev.filter((_,   i) => hasDecay[i]);
      const filteredUrlsCurr  = urlsCurr.filter((_,    i) => hasDecay[i]);
      const filteredMasksCurr = masksCurr.filter((_,   i) => hasDecay[i]);

      // 9) dispatch & set only filtered state
      dispatch(setSlots(newSlots));
      dispatch(setAnnotations(newAnns));
      setAllTypes(filteredTypes);
      setImageLinksPrev(filteredUrlsPrev);
      setPrevDecayMasks(filteredMasksPrev);
      setImageLinksCurr(filteredUrlsCurr);
      setCurrDecayMasks(filteredMasksCurr);
      setCurrIdx(0);

    } catch (err) {
      console.error("ComparisonTimeline error", err);
      dispatch(setSlots({}));
      dispatch(setAnnotations({}));
      setImageLinksPrev([]);
      setPrevDecayMasks([]);
      setImageLinksCurr([]);
      setCurrDecayMasks([]);
    } finally {
      console.log('[ComparisonTimeline] fetchAll complete, turning off loading');
      setImagesLoading(false);
    }
  };

  fetchAll();
}, [selected, patientId, dispatch, router]);



  const allToothNumbers = Object.values(annotations).flatMap((slotAnn) =>
    slotAnn.numbering.map((n) => n.category_id.toString())
  );

  // early returns
  if (visits.length === 0) {
    return (
      <div className="p-6 text-center text-muted-foreground">No visits found.</div>
    );
  }

  if (visits.length === 1) {
    const only = visits[0];
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-playfair text-foreground">
            <Calendar className="w-5 h-5 text-muted-foreground" /> Treatment Progress Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Previous Visit</p>
              <p className="font-medium text-primary">—</p>
            </div>
            <ChevronRight />
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Current Visit</p>
              <p className="font-medium text-foreground">{fmt(only.visitDate)}</p>
            </div>
          </div>
          <p className="mt-4 text-muted-foreground">
            You only have one visit so far. Once you add another, you’ll see the
            full comparison.
          </p>
        </CardContent>
      </Card>
    );
  }

  // early returns...
  if (visits.length < 2) {
    return (
      <div className="p-6 text-center text-muted-foreground">
        {visits.length === 0
          ? "No visits found."
          : `Need at least two visits to compare; you only have ${visits.length}.`}
      </div>
    );
  }

  if (!selected) {
    return (
      <div className="p-6 text-center text-muted-foreground">Loading comparison…</div>
    );
  }

  const { previous, current } = selected;

  const comparisonData = {
    improvements: [
      "Tooth #14 restoration complete",
      "Fluoride treatment applied to tooth #31",
    ],
    newIssues: ["New severe decay detected on tooth #18"],
    progressNotes:
      "Overall oral health has improved with completed restorations. New decay requires immediate attention.",
  };

  // For the “previous” visit:
  const prevToothNumbers = Object.entries(slots)
    .filter(([, img]) => img?.date === previous.visitDate)
    .flatMap(
      ([slotId]) =>
        annotations[slotId]?.numbering.map((n) => n.category_id.toString()) ||
        []
    );

  // Similarly for the “current” visit:
  const currToothNumbers = Object.entries(slots)
    .filter(([, img]) => img?.date === current.visitDate)
    .flatMap(
      ([slotId]) =>
        annotations[slotId]?.numbering.map((n) => n.category_id.toString()) ||
        []
    );

  // URL of the currently-visible “previous” image:
  const prevUrl = imageLinksPrev[currIdx];
  // Find the matching slotId in Redux state:
  const prevSlotId = Object.entries(slots).find(
    ([, img]) => img?.url === prevUrl
  )?.[0];

  // Similarly for the “current” carousel:
  const currUrl = imageLinksCurr[currIdx];
  const currSlotId = Object.entries(slots).find(
    ([, img]) => img?.url === currUrl
  )?.[0];

  // If no slot (or no numbering), default to empty array:
  const prevTeeth = prevSlotId
    ? annotations[prevSlotId]?.numbering.map((n) => n.category_id.toString()) ||
      []
    : [];

  const currTeeth = currSlotId
    ? annotations[currSlotId]?.numbering.map((n) => n.category_id.toString()) ||
      []
    : [];

  // just above your return(...)
  const prevUploadedIndices = imageLinksPrev
    .map((url, i) => (url ? i : -1))
    .filter((i) => i >= 0);
  const currUploadedIndices = imageLinksCurr
    .map((url, i) => (url ? i : -1))
    .filter((i) => i >= 0);

  // where does our `currIdx` sit inside those?
  const prevPos = prevUploadedIndices.indexOf(currIdx);
  const currPos = currUploadedIndices.indexOf(currIdx);

  // how many were uploaded?
  const prevCount = prevUploadedIndices.length;
  const currCount = currUploadedIndices.length;

  function prettifyType(raw?: string): string {
    if (!raw) return "—"; // guard against undefined / empty
    return raw
      .split("_")
      .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
      .join(" ");
  }

// inside ComparisonTimeline, above your JSX:
const handleToothClick = (toothId: number) => {
  // find the first index whose prev or curr mask contains this tooth
  const idx = allTypes.findIndex((_, i) =>
    prevDecayMasks[i]?.some(p => p.category_id === toothId) ||
    currDecayMasks[i]?.some(p => p.category_id === toothId)
  );
  if (idx >= 0) setCurrIdx(idx);
};


  return (
    <div className="space-y-6">
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-playfair text-foreground">
            <Calendar className="w-5 h-5 text-muted-foreground" /> Treatment Progress Comparison
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* — Visit Selector */}
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            {/* Group A */}
            <div className="flex items-center gap-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Previous Visit</p>
                <p className="font-medium text-primary">
                  {fmt(previous.visitDate)}
                </p>
              </div>
              <ChevronRight className="w-5 h-5 text-primary/60" />
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Current Visit</p>
                <p className="font-medium text-foreground">
                  {fmt(current.visitDate)}
                </p>
              </div>
            </div>

            {/* Group B */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => scrollRef.current?.scrollBy({ left: -200 })}
                className="p-1 text-primary disabled:opacity-30"
              >
                <ChevronLeft />
              </button>

              <div
                ref={scrollRef}
                className="visit-timeline flex flex-nowrap gap-2 overflow-x-auto py-1 whitespace-nowrap max-w-md"
                style={{ scrollSnapType: "x mandatory", maxWidth: "37rem" }}
              >
                {visits.map((v) => (
                  <Button
                    key={v.visitId}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelected({ previous: v, current })}
                    className="flex-shrink-0 border-border text-foreground hover:bg-accent"
                    style={{ scrollSnapAlign: "start" }}
                  >
                    {fmt(v.visitDate)}
                  </Button>
                ))}
              </div>

              <button
                onClick={() => scrollRef.current?.scrollBy({ left: 200 })}
                className="p-1 text-primary disabled:opacity-30"
              >
                <ChevronRight />
              </button>
            </div>
          </div>

          {/* X-ray Comparison */}
          <div key={currIdx} className="grid grid-cols-2 gap-6">
            {/* Previous */}
            <div className="space-y-3">
              {/* — pagination bar */}
              <div className="flex justify-between mb-2 text-sm text-muted-foreground">
                {/* Previous panel pagination */}

                {/* <span>{allTypes[currIdx] || "—"}</span> */}
                <span>
                  {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                    prettifyType(allTypes[currIdx])}
                </span>

                <span>
                  Image {prevPos >= 0 ? prevPos + 1 : "0"} of {prevCount}
                </span>
              </div>
              <h4 className="font-medium text-foreground">
                {fmtDate(previous.visitDate)} – <span>Dr. {displayName}</span>
              </h4>
              <div className="relative group aspect-square bg-muted rounded-lg border border-border flex items-center justify-center overflow-hidden">
                {imagesLoading ? (
                  <p className="text-muted-foreground">Loading images…</p>
                ) : (
                  <>
                    {allTypes.length > 1 && (
                      <button
                        onClick={() =>
                          setCurrIdx(
                            (i) => (i - 1 + allTypes.length) % allTypes.length
                          )
                        }
                        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                      >
                        <ChevronLeft className="w-5 h-5 text-white" />
                      </button>
                    )}

                    {imageLinksPrev[currIdx] ? (
                 <DecayOverlayImage
   key={`prev-${currIdx}-${allTypes[currIdx]}`}
                        url={imageLinksPrev[currIdx]!}
                        decay={prevDecayMasks[currIdx] || []}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-center text-muted-foreground">
                        <Calendar className="w-8 h-8 mx-auto mb-2" />
                        {/* <p className="text-sm">No {allTypes[currIdx]} image</p> */}
                        <p className="text-sm">
                          No{" "}
                          {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                            prettifyType(allTypes[currIdx])}{" "}
                          image
                        </p>
                      </div>
                    )}

                    {allTypes.length > 1 && (
                      <button
                        onClick={() =>
                          setCurrIdx((i) => (i + 1) % allTypes.length)
                        }
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                      >
                        <ChevronRight className="w-5 h-5 text-white" />
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Current (same pattern) */}
            <div className="space-y-3">
              {/* Current panel pagination */}
              <div className="flex justify-between mb-2 text-sm text-muted-foreground">
                {/* <span>{allTypes[currIdx] || "—"}</span> */}
                <span>
                  {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                    prettifyType(allTypes[currIdx])}
                </span>

                <span>
                  Image {currPos >= 0 ? currPos + 1 : "0"} of {currCount}
                </span>
              </div>

              <h4 className="font-medium text-foreground">
                {fmtDate(current.visitDate)} – <span>Dr. {displayName}</span>
              </h4>
              <div className="relative group aspect-square bg-muted rounded-lg border border-border flex items-center justify-center overflow-hidden">
                {imagesLoading ? (
                  <p className="text-muted-foreground">Loading images…</p>
                ) : (
                  <>
                    {allTypes.length > 1 && (
                      <button
                        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                        onClick={() =>
                          setCurrIdx(
                            (i) => (i - 1 + allTypes.length) % allTypes.length
                          )
                        }
                      >
                        <ChevronLeft className="w-5 h-5 text-white" />
                      </button>
                    )}

                    {imageLinksCurr[currIdx] ? (
<DecayOverlayImage
   key={`curr-${currIdx}-${allTypes[currIdx]}`}
                        url={imageLinksCurr[currIdx]}
                        decay={currDecayMasks[currIdx] || []}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-center text-muted-foreground">
                        <Calendar className="w-8 h-8 mx-auto mb-2" />
                        {/* <p className="text-sm">No {allTypes[currIdx]} image</p> */}
                        <p className="text-sm">
                          No{" "}
                          {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                            prettifyType(allTypes[currIdx])}{" "}
                          image
                        </p>
                      </div>
                    )}

                    {allTypes.length > 1 && (
                      <button
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                        onClick={() =>
                          setCurrIdx((i) => (i + 1) % allTypes.length)
                        }
                      >
                        <ChevronRight className="w-5 h-5 text-white" />
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="p-4 bg-muted rounded-lg space-y-4">
            <h4 className="flex items-center gap-2 font-medium text-foreground">
              <TrendingUp className="w-4 h-4 text-muted-foreground" /> Treatment Progress Summary
            </h4>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h5 className="text-sm font-medium text-green-600">
                  Improvements
                </h5>
                {prevDecayMasks[currIdx]?.length ? (
                  <ul className="mt-2 space-y-1 text-sm text-green-600">
                    {prevDecayMasks[currIdx].map((pred, i) => {
  const label = DECAY_TYPE_LABELS[pred.category_id] || "Unknown Decay";
  return (
                      <li key={i} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                          <span>
                            Tooth #{pred.category_id}: <strong>{label}</strong>
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No improvements detected
                  </p>
                )}
              </div>

              {/* New Issues = decays from current visit */}
              <div>
                <h5 className="text-sm font-medium text-red-600">New Issues</h5>
                {currDecayMasks[currIdx]?.length ? (
                  <ul className="mt-2 space-y-1 text-sm text-red-600">
                    {currDecayMasks[currIdx].map((pred, i) => {
                      const label =
                        DECAY_TYPE_LABELS[pred.category_id] || "Unknown Decay";
                      return (
                        <li key={i} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full" />
                          <span>
                            Tooth #{pred.category_id}: <strong>{label}</strong>
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="text-sm text-muted-foreground">No new issues detected</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
