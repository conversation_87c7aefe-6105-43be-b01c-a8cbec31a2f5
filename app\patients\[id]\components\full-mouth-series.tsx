"use client";
import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Upload,
  Play,
  Loader2,
  ChevronDown,
  ChevronUp,
  Eye,
} from "lucide-react";
import { FMS_LAYOUT, type XrayImage, type Visit } from "../types";
import { Badge } from "@/components/ui/badge";

import {
  API_BASE_URL,
  GET_ALL_VISITS_WITH_PATIENT_ID,
  PREDICTION_URL,
  REMOVE_UPLOADED_IMAGE,
} from "@/constants/apiRoutes";
import { toast } from "react-toastify";
import {
  addPatientVisitId,
  addPreviousPatientsVisits,
  addPreviousVisitLoadedImagesCompleteData,
  addPreviousVisitLoadedXraysList,
  clearPreviousVisitLoadedImagesCompleteData,
  clearPreviousVisitLoadedXraysList,
  resetPreviousVisitClicked,
  togglePreviousVisitClicked,
} from "@/store/previousVisits";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useAppSelector } from "@/store/hooks";
import { useParams, useRouter } from "next/navigation";
import { selectVisitId } from "@/store/visitSlice";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import {
  setSlots,
  setAnalyzing,
  setUploading,
  setGlobalAnalyzing,
  setIsCollapsed,
  toggleIsCollapsed,
  setPanoramicImage,
  setLoading,
  setError,
  setAnnotations,
  setIsBulkUploading,
  toggleShowDecay,
  toggleShowNumbering,
} from "@/store/fullMouthSeriesSlice";

// Color definitions
const randomColors = {
  TeethNumbering: {
    1: [255, 182, 193, 0.8],
    2: [137, 207, 240, 0.7],
    3: [240, 230, 140, 0.9],
    4: [220, 190, 255, 0.6],
    5: [190, 255, 170, 0.8],
    6: [230, 190, 90, 0.7],
    7: [140, 140, 255, 0.9],
    8: [245, 130, 120, 0.8],
    9: [210, 105, 180, 0.7],
    10: [220, 245, 100, 0.9],
    11: [130, 220, 240, 0.8],
    12: [255, 140, 50, 0.7],
    13: [200, 180, 230, 0.9],
    14: [170, 255, 130, 0.8],
    15: [240, 120, 200, 0.7],
    16: [110, 190, 255, 0.9],
    17: [255, 200, 100, 0.8],
    18: [180, 130, 220, 0.7],
    19: [230, 240, 150, 0.9],
    20: [150, 255, 190, 0.8],
    21: [240, 180, 60, 0.7],
    22: [120, 140, 240, 0.9],
    23: [210, 110, 200, 0.8],
    24: [220, 255, 140, 0.7],
    25: [140, 220, 210, 0.9],
    26: [250, 130, 90, 0.8],
    27: [190, 240, 100, 0.7],
    28: [110, 160, 230, 0.9],
    29: [230, 200, 120, 0.8],
    30: [240, 140, 180, 0.7],
    31: [160, 255, 220, 0.9],
    32: [200, 120, 240, 0.8],
  },
  TeethDecay: {
    1: [255, 245, 157, 0.8],
    2: [255, 215, 0, 0.8],
    3: [255, 105, 180, 0.8],
    4: [255, 0, 0, 0.9],
  },
};

interface Prediction {
  conf_score: number;
  category_id: number;
  segmentation: number[] | number[][];
  annotation_source?: string;
}

interface ImageData {
  annotation_source: any;
  image_id: number;
  image_type: string;
  predictions: {
    teeth_numbering: Prediction[];
    teeth_decay: Prediction[];
  };
}

interface ApiResponse {
  isError: boolean;
  statusCode: number;
  data: ImageData[];
}

interface Annotation {
  decay: Prediction[];
  numbering: Prediction[];
}

interface FullMouthSeriesProps {
  annotatorOpen: any;
  handleload: any;
  setIsUploading: any;
  onImagesLoad: any;
  onVisitSelect: any;
  isAnalyzing: any;
  analysisProgress: any;
  analysisStatus: any;
  setUploadStatus: any;
  setUploadProgress: any;
  onImageAnalyze: (slotId: string, image: XrayImage) => void;
  onOpenAnnotator: (
    slotId: string,
    image: XrayImage,
    annotations: Annotation,
    callback?: () => void
  ) => void;
  selectedVisit?: Visit;
  isUploading: any;
  uploadProgress: any;
  uploadStatus: any;
  onBulkUpload: (files: FileList | null, visitId?: string) => Promise<any>;
  onBulkUploadClick: () => void;
  onRunAnalysisClick: () => void;
  onRegisterRunAnalysis?: (fn: () => Promise<void>) => void;
  setslots: (slots: any) => void;
  setAnalysisStatus: any;
  setIsAnalyzing: any;
  setAnalysisProgress: any;
  currentid: any;
  setcurrentid: (id: any) => void;


}

export function FullMouthSeries({
  setAnalysisStatus,
  setIsAnalyzing,
  setAnalysisProgress,
  currentid,
  setcurrentid,
  handleload,
  annotatorOpen,
  onImagesLoad,
  setIsUploading,
  onVisitSelect,
  setUploadProgress,
  setUploadStatus,
  onImageAnalyze,
  onOpenAnnotator,
  selectedVisit,
  onBulkUpload,
  onBulkUploadClick,
  onRunAnalysisClick,
  onRegisterRunAnalysis,
  setslots,
  isUploading,
  uploadProgress,
  uploadStatus,
  isAnalyzing,
  analysisProgress,
  analysisStatus,

}: FullMouthSeriesProps) {
  const visitId = useAppSelector(selectVisitId);
  const params = useParams();
  const router = useRouter();
  const patientId = params.id as string;
  const showDecay = useSelector((state: RootState) => state.fullMouthSeries.showDecay ?? true);
  const showNumbering = useSelector((state: RootState) => state.fullMouthSeries.showNumbering ?? false);
  const [panoramicOpen, setPanoramicOpen] = useState(false);
  const [miscOpen, setMiscOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "intraoral" | "panoramic" | "misc"
  >("intraoral");
  const patientVisitId = useSelector(
    (state: RootState) => state.previousVisit.visitId
  );
  const previousVisitsImages = useSelector(
    (state: RootState) => state.previousVisit.previousVisitLoadedXraysList
  );
  const previousVisitsImagesClicked = useSelector(
    (state: RootState) => state.previousVisit.previousVisitClicked
  );
  const dispatch = useDispatch();
  const userId = useSelector((state: RootState) => state.user.userId);
  const displayName = useSelector((state: RootState) => state.user.displayName);
   const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
const panoramicImage = useSelector((state: RootState) => state.fullMouthSeries.panoramicImage);
const annotations = useSelector((state: RootState) => state.fullMouthSeries.annotations);
  // const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  const panoramicCount = slots.PANORAMIC ? 1 : 0;
  const miscCount = Object.keys(slots).filter(
    (key) => key !== "PANORAMIC" && !FMS_LAYOUT.some((s) => s.id === key)
  ).length;
  const analyzing = useSelector(
    (state: RootState) => state.fullMouthSeries.analyzing
  );
  const [loadingImages, setLoadingImages] = useState<{
    [key: string]: boolean;
  }>({});
  const uploading = useSelector(
    (state: RootState) => state.fullMouthSeries.uploading
  );
  const bulkUploading = useSelector(
    (state: RootState) => state.fullMouthSeries.bulkUploading
  );
  const globalAnalyzing = useSelector(
    (state: RootState) => state.fullMouthSeries.globalAnalyzing
  );
  const isCollapsed = useSelector(
    (state: RootState) => state.fullMouthSeries.isCollapsed
  );
  // const panoramicImage = useSelector(
  //   (state: RootState) => state.fullMouthSeries.panoramicImage
  // );
  const loading = useSelector(
    (state: RootState) => state.fullMouthSeries.loading
  );
  const error = useSelector((state: RootState) => state.fullMouthSeries.error);
  // const annotations = useSelector(
  //   (state: RootState) => state.fullMouthSeries.annotations
  // );
  const canvasRefs = useRef<Record<string, HTMLCanvasElement | null>>({});


  const [cid, setcid] = useState(0);


  const hasDecay = (slotId: string) => {
    const key = slotId.toUpperCase();
    return annotations[key]?.decay?.length > 0;
  };
  const normalizeCoordinates = (
    point: { x: number; y: number },
    imageElement: HTMLImageElement,
    slotId?: string
  ) => {
    const rect = imageElement.getBoundingClientRect();
    const imageWidth = imageElement.naturalWidth;
    const imageHeight = imageElement.naturalHeight;

    let displayedWidth = rect.width;
    let displayedHeight = rect.height;
    let offsetX = 0;
    let offsetY = 0;

    // For panoramic images with object-contain, calculate actual displayed image dimensions and offsets
    if (slotId === "PANORAMIC") {
      const containerWidth = rect.width;
      const containerHeight = rect.height;

      // Calculate the scale factor for object-contain
      const scaleX = containerWidth / imageWidth;
      const scaleY = containerHeight / imageHeight;
      const scale = Math.min(scaleX, scaleY);

      displayedWidth = imageWidth * scale;
      displayedHeight = imageHeight * scale;

      // Calculate centering offsets for object-contain
      offsetX = (containerWidth - displayedWidth) / 2;
      offsetY = (containerHeight - displayedHeight) / 2;
    }

    // Calculate scale factors to map from natural image coordinates to displayed coordinates
    const scaleX = displayedWidth / imageWidth;
    const scaleY = displayedHeight / imageHeight;

    // Apply scaling and add centering offset for panoramic images
    const x = point.x * scaleX + offsetX;
    const y = point.y * scaleY + offsetY;

    // Clamp coordinates to container bounds
    return {
      x: Math.max(0, Math.min(x, rect.width)),
      y: Math.max(0, Math.min(y, rect.height)),
    };
  };

  const drawAnnotations = (
    slotId: string,
    canvas: HTMLCanvasElement,
    imageElement: HTMLImageElement
  ) => {
    const ctx = canvas.getContext("2d");
    if (!ctx || !imageElement.complete) {
      // // console.log(
      //   `Cannot draw annotations for ${slotId}: ctx or image not ready`
      // );
      return;
    }
    // Canvas dimensions are already set in initializeCanvas, just clear and prepare for drawing
    const displayedWidth = canvas.width;
    const displayedHeight = canvas.height;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.save();
    ctx.beginPath();
    ctx.rect(0, 0, displayedWidth, displayedHeight);
    ctx.clip();

    const drawPolygon = (
      finding: Prediction,
      colorMap: Record<number, number[]>,
      isNumbering: boolean = false
    ) => {
      if (!finding.segmentation || !Array.isArray(finding.segmentation)) {
        // console.log(
        //   `Invalid segmentation for ${finding.annotation_source || "unknown"}:`,
        //   finding.segmentation
        // );
        return;
      }
      const segmentations = Array.isArray(finding.segmentation[0])
        ? finding.segmentation
        : [finding.segmentation];
      segmentations.forEach((points, index) => {
        if (
          !Array.isArray(points) ||
          points.length < 4 ||
          points.length % 2 !== 0
        ) {
          // console.log(
          //   `Invalid points for ${
          //     finding.annotation_source || "unknown"
          //   }, segment ${index}:`,
          //   points
          // );
          return;
        }
        const normalizedPoints = [];
        ctx.beginPath();
        for (let i = 0; i < points.length; i += 2) {
          const x = points[i];
          const y = points[i + 1];
          if (
            typeof x !== "number" ||
            typeof y !== "number" ||
            isNaN(x) ||
            isNaN(y)
          ) {
            // console.log(
            //   `Invalid coordinate pair at index ${i} for ${
            //     finding.annotation_source || "unknown"
            //   }:`,
            //   { x, y }
            // );
            return;
          }
          const point = normalizeCoordinates({ x, y }, imageElement, slotId);
          normalizedPoints.push(point);
          if (i === 0) {
            ctx.moveTo(point.x, point.y);
          } else {
            ctx.lineTo(point.x, point.y);
          }
        }
        ctx.closePath();
        const color = colorMap[finding.category_id] || [255, 0, 0, 0.5];
        if (isNumbering) {
          ctx.fillStyle = "transparent";
          ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.stroke();
          const cx =
            normalizedPoints.reduce((s, p) => s + p.x, 0) /
            normalizedPoints.length;
          const cy =
            normalizedPoints.reduce((s, p) => s + p.y, 0) /
            normalizedPoints.length;
          ctx.fillStyle = "#000";
          ctx.font = "bold 14px Arial";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText(String(finding.category_id), cx, cy);
        } else {
          ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.5)`;
          ctx.fill();
          ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.stroke();
        }
      });
    };

    if (showNumbering) {
      const numberingFindings = annotations[slotId]?.numbering || [];
      numberingFindings.forEach((finding) =>
        drawPolygon(finding, randomColors.TeethNumbering, true)
      );
    }
    if (showDecay) {
      const decayFindings = annotations[slotId]?.decay || [];
      decayFindings.forEach((finding) =>
        drawPolygon(finding, randomColors.TeethDecay)
      );
    }
    ctx.restore();
  };

  const initializeCanvas = (
    slotId: string,
    canvas: HTMLCanvasElement | null,
    imageElement: HTMLImageElement | null
  ) => {
    if (!canvas || !imageElement) return;
    canvasRefs.current[slotId] = canvas;

    const redraw = () => {
      const rect = imageElement.getBoundingClientRect();

      if (slotId === "PANORAMIC") {
        // For panoramic images with object-contain, set canvas size to match container
        canvas.width = rect.width;
        canvas.height = rect.height;

        // Update canvas position to match container
        canvas.style.width = `${rect.width}px`;
        canvas.style.height = `${rect.height}px`;
      } else {
        // For other images, canvas matches image display size exactly
        canvas.width = rect.width;
        canvas.height = rect.height;
        canvas.style.width = `${rect.width}px`;
        canvas.style.height = `${rect.height}px`;
      }

      drawAnnotations(slotId, canvas, imageElement);
    };

    imageElement.onload = redraw;
    if (imageElement.complete) {
      // Force immediate redraw for already loaded images
      setTimeout(redraw, 0);
    }

    const resizeObserver = new ResizeObserver(() => {
      redraw();
    });
    resizeObserver.observe(imageElement);
    return () => resizeObserver.disconnect();
  };

  useEffect(() => {
    Object.entries(canvasRefs.current).forEach(([slotId, canvas]) => {
      if (!canvas) return;

      // skip any non-element siblings (e.g. text nodes)
      const el = canvas.previousElementSibling;
      if (!(el instanceof HTMLImageElement)) return;

      // now it's safe to read el.complete
      if (el.complete) {
        drawAnnotations(slotId, canvas, el);
      }
    });
  }, [annotations, showDecay, showNumbering]);

  // Additional effect to ensure annotations are redrawn when component mounts or annotations change
  useEffect(() => {
    const redrawAllAnnotations = () => {
      Object.entries(canvasRefs.current).forEach(([slotId, canvas]) => {
        if (!canvas) return;
        const imageElement = canvas.previousElementSibling as HTMLImageElement;
        if (imageElement && imageElement.complete) {
          drawAnnotations(slotId, canvas, imageElement);
        }
      });
    };

    // Small delay to ensure DOM is ready
    const timeoutId = setTimeout(redrawAllAnnotations, 100);
    return () => clearTimeout(timeoutId);
  }, [annotations]);

  // Effect to reset show states when no annotations are available
  useEffect(() => {
    const hasDecay = Object.values(annotations).some(
      (annotation) => annotation?.decay && annotation.decay.length > 0
    );
    const hasNumbering = Object.values(annotations).some(
      (annotation) => annotation?.numbering && annotation.numbering.length > 0
    );

    // Reset showDecay if no decay annotations are available and it's currently shown
    if (!hasDecay && showDecay) {
      dispatch(toggleShowDecay());
    }

    // Reset showNumbering if no numbering annotations are available and it's currently shown
    if (!hasNumbering && showNumbering) {
      dispatch(toggleShowNumbering());
    }
  }, [annotations, showDecay, showNumbering, dispatch]);

  const visits = useSelector(
    (state: RootState) => state.previousVisit.previousPatientVisits
  );
  // console.log("visits", visits);

  const handleCardClick = async (slotId: string) => {
    const image =
      slots[slotId] || (slotId === "PANORAMIC" ? panoramicImage : null);
    if (!image) return;
    if (!image.analyzed) {
      await handleAnalyze(slotId);
    }
    const analyzedImage = slots[slotId] || panoramicImage;
    if (analyzedImage?.analyzed) {
      onOpenAnnotator(
        slotId,
        analyzedImage,
        annotations[slotId] || { decay: [], numbering: [] },
        () => {
          const canvas = canvasRefs.current[slotId];
          const imageElement = canvas?.previousSibling as HTMLImageElement;
          if (canvas && imageElement && imageElement.complete) {
            drawAnnotations(slotId, canvas, imageElement);
          }
        }
      );
    }
  };

  // console.log("visitsss", currentid?.visitId);
  var vid = currentid?.visitId;

  useEffect(() => {
    setcid(currentid?.visitId);
  }, [currentid?.visitId]);

  const mockApiCall = async (
    images: { slotId: string; image: XrayImage }[],
    visitId: any,
    maxRetries: number = 2,
    retryDelayMs: number = 1000
  ): Promise<ApiResponse> => {
    // Validate all images have imageId
    const invalidImages = images.filter(({ image }) => !image?.imageId);
    if (invalidImages.length > 0) {
      console.error("Images missing imageId:", invalidImages);
      return { isError: true, statusCode: 400, data: [] };
    }

    const attemptFetch = async (retryCount: number): Promise<ApiResponse> => {
      try {
        const response = await fetchWithRefresh(
          `${PREDICTION_URL}${visitId}`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              images: images.map(({ slotId, image }) => ({
                imageId: image.imageId.toString(), // Ensure string conversion
                imageType: slotId,
              })),
            }),
            credentials: "include",
          },
          router
        );

        if (!response) {
          console.error("Session expired or refresh failed");
          return { isError: true, statusCode: 401, data: [] };
        }

        if (response.status === 401 || response.status === 504) {
          if (retryCount > 0) {
            await new Promise((resolve) => setTimeout(resolve, retryDelayMs));
            return await attemptFetch(retryCount - 1);
          }
          throw new Error(`API request failed with status ${response.status}`);
        }

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        try {
          const result = await response.json();
          return { isError: false, statusCode: response.status, data: result };
        } catch (jsonError) {
          console.error("JSON parsing error:", jsonError);
          return { isError: true, statusCode: response.status, data: [] };
        }
      } catch (error: any) {
        console.error("Error in mockApiCall:", error);
        return { isError: true, statusCode: 500, data: [] };
      }
    };

    return await attemptFetch(maxRetries);
  };

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const handleGlobalAnalysis = useCallback(async () => {
  const imagesToAnalyze = [
    ...Object.entries(slots)
      .filter(([_, image]) => image && !image.analyzed)
      .map(([slotId, image]) => ({ slotId, image: image! })),
    ...(panoramicImage && !panoramicImage.analyzed
      ? [{ slotId: "PANORAMIC", image: panoramicImage }]
      : []),
  ];

  if (imagesToAnalyze.length === 0) return;

  dispatch(setGlobalAnalyzing(true));
  const newAnalyzing = { ...analyzing };
  imagesToAnalyze.forEach(({ slotId }) => {
    newAnalyzing[slotId] = true;
  });
  dispatch(setAnalyzing(newAnalyzing));

  try {
   setAnalysisStatus("Processing and analyzing uploaded images...");
    setAnalysisProgress(10);

    const apiCallPromise = mockApiCall(imagesToAnalyze, patientVisitId);

    // Smooth progress from 10% to 99% while waiting
    let currentProgress = 10;
    const progressInterval = setInterval(() => {
      if (currentProgress < 99) {
        currentProgress += 1;
        setAnalysisProgress(currentProgress);
      }
    }, 200);

    const response = await apiCallPromise;
    clearInterval(progressInterval);

    if (response.isError) {
      console.error("Analysis failed:", response);
      setAnalysisStatus("Analysis failed");
      setAnalysisProgress(0);
      return;
    }

    setAnalysisStatus("Processing results...");
    setAnalysisProgress(99); // before final step

    const newAnnotations = { ...annotations };
    let hasResults = false;

    for (const { slotId, image } of imagesToAnalyze) {
      const matches = response.data.filter(
        (item) =>
          item.image_id === Number(image.imageId) &&
          item.image_type.toUpperCase() === slotId
      );

      if (matches.length > 0) {
        hasResults = true;
        const combinedNumbering: Prediction[] = [];
        const combinedDecay: Prediction[] = [];

        matches.forEach((item) => {
          (item.predictions.teeth_numbering || []).forEach((pred) =>
            combinedNumbering.push({
              ...pred,
              annotation_source: item.annotation_source,
            })
          );
          (item.predictions.teeth_decay || []).forEach((pred) =>
            combinedDecay.push({
              ...pred,
              annotation_source: item.annotation_source,
            })
          );
        });

        newAnnotations[slotId] = {
          numbering: combinedNumbering,
          decay: combinedDecay,
        };
      }
    }

    if (hasResults) {
      dispatch(setAnnotations(newAnnotations));
// console.log("New annotations after global analysis:", newAnnotations);

      const newSlots = { ...slots };
      imagesToAnalyze.forEach(({ slotId, image }) => {
        newSlots[slotId] = { ...image, analyzed: true };
      });
      dispatch(setSlots(newSlots));

      if (panoramicImage && !panoramicImage.analyzed) {
        dispatch(setPanoramicImage({ ...panoramicImage, analyzed: true }));
      }

      imagesToAnalyze.forEach(({ slotId, image }) =>
        onImageAnalyze(slotId, { ...image, analyzed: true })
      );

      if (!response.isError && currentid?.visitId) {
        loadVisitImages();
      }

      setAnalysisStatus("Analysis complete");
      setAnalysisProgress(100);
    } else {
      setAnalysisStatus("No analysis results found");
      setAnalysisProgress(100);
    }
  } catch (err) {
    console.error("Global analysis error:", err);
    setAnalysisStatus("Analysis failed");
    setAnalysisProgress(0);
  } finally {
    const finalAnalyzing = { ...analyzing };
    imagesToAnalyze.forEach(({ slotId }) => {
      finalAnalyzing[slotId] = false;
    });
    dispatch(setAnalyzing(finalAnalyzing));
    dispatch(setGlobalAnalyzing(false));

    setTimeout(() => {
      setAnalysisStatus(null);
      setAnalysisProgress(0);
    }, 1000);
  }
}, [slots, panoramicImage, analyzing, annotations, cid, annotatorOpen, currentid, displayName]);



  const analysisRef = useRef(handleGlobalAnalysis);

  useEffect(() => {
    analysisRef.current = handleGlobalAnalysis;
  }, [handleGlobalAnalysis]);

  useEffect(() => {
    onRegisterRunAnalysis?.(() => analysisRef.current());
  }, [onRegisterRunAnalysis]);

  async function handleAnalyze(slotId: string) {
    const image =
      slots[slotId] || (slotId === "PANORAMIC" ? panoramicImage : null);
    if (!image) return;
    dispatch(setAnalyzing({ ...analyzing, [slotId]: true }));
    try {
      const response = await mockApiCall([{ slotId, image }], patientVisitId); // Use the latest visitId from Redux store
      if (response.isError || response.statusCode !== 200) {
        throw new Error("API analysis failed");
      }
      const matchingData = response.data.filter(
        (item) =>
          item.image_id === Number(image.imageId) &&
          item.image_type.toUpperCase() === slotId
      );

      const combinedPredictions = {
        teeth_numbering: [] as Prediction[],
        teeth_decay: [] as Prediction[],
      };

      //  console.log(
      //   `📊 [${slotId}] decay preds before merge:`,
      //   response.data.flatMap((i) => i.predictions.teeth_decay || []).length
      // );

      matchingData.forEach((item) => {
        if (item.predictions.teeth_numbering) {
          const numberedWithSource = item.predictions.teeth_numbering.map(
            (pred) => ({
              ...pred,
              annotation_source: item.annotation_source,
            })
          );
          combinedPredictions.teeth_numbering.push(...numberedWithSource);
        }
        if (item.predictions.teeth_decay) {
          const decayWithSource = item.predictions.teeth_decay.map((pred) => ({
            ...pred,
            annotation_source: item.annotation_source,
          }));
          combinedPredictions.teeth_decay.push(...decayWithSource);
        }
      });

      const key = slotId.toUpperCase();

      // Get existing annotations for this slot from Redux
      const existingSlotAnnotations = annotations[key] || { numbering: [], decay: [] };

      // Filter out existing AI annotations, as they will be replaced by new API data
      const preservedManualNumbering = existingSlotAnnotations.numbering.filter(ann => ann.annotation_source === "Manual");
      const preservedManualDecay = existingSlotAnnotations.decay.filter(ann => ann.annotation_source === "Manual");

      // Merge preserved manual annotations with newly fetched API annotations
      // For decay, use a Map to ensure uniqueness based on segmentation (assuming segmentation is unique enough)
      const uniqueDecayMap = new Map<string, Prediction>();
      [...preservedManualDecay, ...combinedPredictions.teeth_decay].forEach(ann => {
          const key = `${ann.category_id}-${JSON.stringify(ann.segmentation)}`;
          uniqueDecayMap.set(key, ann);
      });
      const uniqueDecay = Array.from(uniqueDecayMap.values());

      // For numbering, assuming category_id is unique per tooth
      const uniqueNumberingMap = new Map<number, Prediction>();
      [...preservedManualNumbering, ...combinedPredictions.teeth_numbering].forEach(ann => {
          uniqueNumberingMap.set(ann.category_id, ann);
      });
      const uniqueNumbering = Array.from(uniqueNumberingMap.values());

      dispatch(
        setAnnotations({
          ...annotations,
          [key]: {
            decay: uniqueDecay,
            numbering: uniqueNumbering,
          },
        })
      );

      const analyzedImage = { ...image, analyzed: true };
      if (slotId === "PANORAMIC") {
        dispatch(setPanoramicImage(analyzedImage));
        dispatch(setSlots({ ...slots, [slotId]: analyzedImage }));
      } else {
        dispatch(setSlots({ ...slots, [slotId]: analyzedImage }));
      }
      onImageAnalyze(slotId, analyzedImage);

      const canvas = canvasRefs.current[slotId];
      const imageElement = canvas?.previousSibling as HTMLImageElement;
      if (canvas && imageElement && imageElement.complete) {
        drawAnnotations(slotId, canvas, imageElement);
      }

      // Ensure the UI updates consistently with global analysis
      if (!response.isError && patientVisitId) {
        loadVisitImages();
      }
    } catch (err) {
      console.error("Analysis error:", err);
    } finally {
      dispatch(setAnalyzing({ ...analyzing, [slotId]: false }));
    }
  }


  const loadVisitImages = useCallback(() => {
    const currentImages = previousVisitsImages || []; // Ensure it's always an array

    if (currentImages.length === 0) {
      dispatch(setSlots({})); // Dispatch empty slots when no images
      return;
    }

    const newSlots: Record<string, XrayImage> = {};
    currentImages.forEach((xray) => {
      const slotId = xray.type; // Use xray.type as slotId
      const hasAnnotations = annotations[slotId.toUpperCase()] &&
                             (annotations[slotId.toUpperCase()].decay?.length > 0 ||
                              annotations[slotId.toUpperCase()].numbering?.length > 0);

      if (slotId === "PANORAMIC") {
        newSlots.PANORAMIC = { ...xray, analyzed: hasAnnotations };
      } else {
        const slot = FMS_LAYOUT.find((s) => s.id === slotId);
        if (slot) {
          newSlots[slot.id] = { ...xray, analyzed: hasAnnotations };
        } else {
          newSlots[slotId] = { ...xray, analyzed: hasAnnotations };
        }
      }
    });
    dispatch(setSlots(newSlots));
  }, [previousVisitsImages, dispatch, annotations]);

  useEffect(() => {
    // Load visit images if a new visit is selected, or if previousVisitsImages contains data.
    // This prevents clearing the displayed images when navigating back from the AI annotator,
    // as the `slots` state should persist in Redux.
    if (selectedVisit || (previousVisitsImages && previousVisitsImages.length > 0)) {
      loadVisitImages();
    }
  }, [selectedVisit, loadVisitImages, previousVisitsImages]);

  const handleFileUpload = async (
    slotId: string,
    files: FileList | null,
    router: any
  ) => {
    setIsUploading(true);
    setUploadStatus("Preparing upload...");
    setUploadProgress(5);

    if (!files || files.length === 0 || !patientId) return;

    try {
      let slotInfo;
      if (slotId === "PANORAMIC") {
        slotInfo = { id: "PANORAMIC", type: "PANORAMIC", position: "" };
      } else {
        slotInfo = FMS_LAYOUT.find((s) => s.id === slotId) || {
          id: slotId,
          type: slotId,
          position: "",
        };
      }

      setUploadStatus("Checking existing visits...");
      setUploadProgress(10);

      const visitsResponse = await fetchWithRefresh(
        `${GET_ALL_VISITS_WITH_PATIENT_ID}${patientId}`,
        {
          method: "GET",
          credentials: "include",
        },
        router
      );

      if (!visitsResponse || !visitsResponse.ok) {
        console.error("Failed to fetch visits");
        throw new Error("Failed to fetch visits");
      }

      const visits = await visitsResponse.json();
      // console.log("Visits response:", visits);

      // Get current date in local time (without time part)
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0); // Normalize to start of day

      const latestVisit =
        visits.length > 0
          ? visits.reduce((latest: any, visit: any) => {
              const visitDate = new Date(visit.visitDate);
              visitDate.setHours(0, 0, 0, 0); // Normalize to start of day
              const latestDate = new Date(latest.visitDate);
              latestDate.setHours(0, 0, 0, 0);
              return visitDate > latestDate ? visit : latest;
            })
          : null;

      // Compare dates properly
      const isSameDate = (date1: Date, date2: Date) => {
        return (
          date1.getFullYear() === date2.getFullYear() &&
          date1.getMonth() === date2.getMonth() &&
          date1.getDate() === date2.getDate()
        );
      };

      const useExistingVisit = latestVisit
        ? isSameDate(new Date(latestVisit.visitDate), currentDate)
        : false;

      //  console.log("Latest visit details:", {
      //   latestVisit,
      //   latestVisitDate: latestVisit ? new Date(latestVisit.visitDate) : null,
      //   currentDate,
      //   useExistingVisit,
      // });

      const visitId = useExistingVisit ? latestVisit.visitId : null;
      // console.log("Visit ID in File Upload", visitId);

      const apiEndpoint = visitId
        ? `${API_BASE_URL}/api/ImageUploader/uploadImagesToVisit`
        : `${API_BASE_URL}/api/ImageUploader/createVisitAndUploadImages`;

        //  const apiEndpoint = visitId
        // ? "https://localhost:7169/api/ImageUploader/uploadImagesToVisitT"
        // : "https://localhost:7169/api/ImageUploader/createVisitAndUploadImagesT"; 

      dispatch(setUploading({ ...uploading, [slotId]: true }));

      setUploadStatus("Preparing files...");
      setUploadProgress(20);

      const formData = new FormData();
      Array.from(files).forEach((file) => {
        formData.append("images", file);
        formData.append("imageTypes", slotId);
      });

      formData.append("patientId", String(patientId));
      if (visitId) {
        formData.append("patientVisitId", String(visitId));
      }
      formData.append("createdBy", String(userId));

      //  console.log(
      //   "FormData:",
      //   formData,
      //   "Files:",
      //   files,
      //   "API Endpoint:",
      //   apiEndpoint
      // );

      setUploadStatus("Uploading images...");
      setUploadProgress(30);

      const xhr = new XMLHttpRequest();
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round(
            (event.loaded / event.total) * 100
          );
          setUploadProgress(30 + percentComplete * 0.5);
        }
      });

      const response = await new Promise<any>((resolve, reject) => {
        xhr.open("POST", apiEndpoint, true);
        xhr.withCredentials = true;

        const token = localStorage.getItem("accessToken");
        if (token) {
          xhr.setRequestHeader("Authorization", `Bearer ${token}`);
        }

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              resolve(xhr.responseText);
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        };

        xhr.onerror = () => {
          reject(new Error("Upload failed due to network error"));
        };

        xhr.send(formData);
      });

      setUploadStatus("Processing images...");
      setUploadProgress(80);
  setUploadProgress(85);
    setUploadProgress(90);
      setUploadProgress(80);
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }

      if (response.Uploads && response.Uploads.length) {
        const upload = response.Uploads[0];
        const newImage: XrayImage = {
          id: upload.imageType,
          imageId: upload.id,
          url: upload.url,
          type: slotInfo.type,
          position: slotInfo.position,
          date: new Date().toISOString(),
          analyzed: false,
          status: upload?.status,
          imageType: upload?.imageType,
          findings: [],
        };
        // console.log("handle File Upload New Image:", newImage);

        if (slotId === "PANORAMIC") {
          dispatch(setPanoramicImage(newImage));
          dispatch(setSlots({ ...slots, [upload.ImageType]: newImage }));
        } else {
          dispatch(setSlots({ ...slots, [slotId]: newImage }));
        }

        await fetchVisits();
      }

      if (response?.uploads) {
        const newSlots: Record<string, XrayImage> = {};
        let newPanoramicImage: XrayImage | null = null;

        response.uploads.forEach((xray: any) => {
          if (xray.imageType === "PANORAMIC") {
            newSlots.PANORAMIC = xray;
          } else {
            const slot = FMS_LAYOUT.find((s) => s.id === xray.imageType);
            if (slot) {
              newSlots[slot.id] = xray;
            } else {
              newSlots[xray.imageType] = xray;
            }
          }
        });
        dispatch(setSlots(newSlots));
        await fetchVisits();
        // console.log("Slots after bulk upload:", newSlots);
      }

      const counts = response.uploads.reduce(
        (acc: any, { imageType }: any) => {
          if (imageType === "PANORAMIC") acc.panoramic++;
          else if (imageType.startsWith("MISL")) acc.miscellaneous++;
          else acc.intraoral++;
          return acc;
        },
        { panoramic: 0, intraoral: 0, miscellaneous: 0 }
      );

      // // Filter non-zero counts and format message
      const messageParts = [
        counts.panoramic && `${counts.panoramic} Panoramic`,
        counts.intraoral && `${counts.intraoral} Intraoral`,
        counts.miscellaneous && `${counts.miscellaneous} Miscellaneous`,
      ]
        .filter(Boolean)
        .join(", ");

      // Show toast immediately (top-right by default)
      // console.log("messageParts", messageParts);
      toast.success(`Upload Successfull!`);
      const patid = params.id as string;

      // console.log("patid", patid);
      // console.log("visitId", visitId);
      // console.log("visits", visits);

      // console.log("visits", visits);
      function parseDate(dateStr: any) {
        const [month, day, year] = dateStr.split("/").map(Number);
        return new Date(year, month - 1, day);
      }

      let latestVisits = visits[0];
      for (let i = 1; i < visits.length; i++) {
        const currentDate = parseDate(visits[i].visitDate);
        const latestDate = parseDate(latestVisits.visitDate);
        if (currentDate > latestDate) {
          latestVisits = visits[i];
        }
      }

      // console.log("example", latestVisits.visitDate);
      const datePart = latestVisits.visitDate.split("T")[0];
      // console.log("dpart", datePart);
      const [year, month, day] = datePart.split("-");

      const formattedDate = `${parseInt(month)}/${parseInt(day)}/${year}`;
      // console.log(formattedDate);

      const res = await fetchWithRefresh(
        `${API_BASE_URL}/api/ImageUploader/image-links/${patientId}/${visitId}/visitDate?visitDate=${formattedDate}`,
        { method: "GET", credentials: "include" },
        router
      );

      const data = await res?.json();
      // console.log("Loaded Data from previous Visits", data);
      dispatch(addPreviousVisitLoadedImagesCompleteData(data));

      const xrays = data.results.map((result: any) => ({
        id: result.url.split("/").pop()?.split(".")[0] || `${Date.now()}`,
        imageId: result.id,
        url: result.url,
        type: result.type,
        date: new Date().toISOString(),
        analyzed: false,
        findings: [],
      }));

      dispatch(addPatientVisitId(parseInt(visitId, 10)));
      dispatch(addPreviousVisitLoadedXraysList(xrays));
      dispatch(togglePreviousVisitClicked());

      // console.log("Loaded Xrays:", xrays);

      if (onImagesLoad) {
        onImagesLoad(xrays);
      }
      const latestVisitByReduce = visits.reduce((latest: any, current: any) =>
        new Date(current.visitDate) > new Date(latest.visitDate)
          ? current
          : latest
      );

      // console.log("Latest visit (using reduce):", latestVisitByReduce);
      const updatedVisit = {
        ...latestVisitByReduce,
        xrays,
        imageCount: data.total,
      };
      onVisitSelect(updatedVisit);

      setUploadStatus("Upload complete!");
      setUploadProgress(100);

      await fetchVisits();
    } catch (error: any) {
      console.error("Error uploading image:", error);
      setUploadStatus("Upload complete!");
    } finally {
      setLoadingImages((prev) => ({ ...prev, [visitId]: false }));
      dispatch(setUploading({ ...uploading, [slotId]: false }));
      setTimeout(() => {
        setIsUploading(false);
        setUploadStatus(null);
        setUploadProgress(0);
      }, 1500);
    }
  };


const handleBulkUpload = async (files: FileList | null) => {
    if (!files) return;
    // console.log("Bulk Upload Triggered in fullMouthSeries.tsx");

    try {
      dispatch(setIsBulkUploading(true));
      const intraoralIds = FMS_LAYOUT.map((s) => s.id);
      const emptySlots = intraoralIds.filter((id) => !slots[id]);
      let miscCounter = Object.keys(slots).filter(
        (key) => key !== "PANORAMIC" && !intraoralIds.includes(key)
      ).length;
     
      const result = await onBulkUpload(files, visitId?.toString());
      // console.log("Bulk upload result:", result);
     
      // 1. Get the visitId from the response
      const newVisitId = result?.visitId || visitId;
      if (!newVisitId) {
        throw new Error("No visit ID returned from upload");
      }
     
      // 2. Dispatch the visit ID to Redux immediately
      dispatch(addPatientVisitId(parseInt(newVisitId, 10)));
 
      if (result?.uploads) {
        const newSlots: Record<string, XrayImage> = {};
        result.uploads.forEach((xray: any) => {
          if (xray.imageType === "PANORAMIC") {
            newSlots.PANORAMIC = xray;
          } else {
            const slot = FMS_LAYOUT.find((s) => s.id === xray.imageType);
            if (slot) {
              newSlots[slot.id] = xray;
            } else {
              newSlots[xray.imageType] = xray;
            }
          }
        });
        dispatch(setSlots(newSlots));
        await fetchVisits();
      }
 
      const visitsResponse = await fetchWithRefresh(
        `${GET_ALL_VISITS_WITH_PATIENT_ID}${patientId}`,
        { method: "GET", credentials: "include" },
        router
      );
      if (!visitsResponse || !visitsResponse.ok) {
        throw new Error("Failed to fetch visits");
      }
     
      const visits = await visitsResponse.json();
      const latestVisit = visits.reduce((latest: any, current: any) =>
        new Date(current.visitDate) > new Date(latest.visitDate) ? current : latest
      );
 
      const datePart = latestVisit.visitDate.split("T")[0];
      const [year, month, day] = datePart.split("-");
      const formattedDate = `${parseInt(month)}/${parseInt(day)}/${year}`;
 
      // 3. Use the newVisitId in the API call
      const res = await fetchWithRefresh(
        `${API_BASE_URL}/api/ImageUploader/image-links/${patientId}/${newVisitId}/visitDate?visitDate=${formattedDate}`,
        { method: "GET", credentials: "include" },
        router
      );
 
      const data = await res?.json();
      const xrays = data.results.map((result: any) => ({
        id: result.url.split("/").pop()?.split(".")[0] || `${Date.now()}`,
        imageId: result.id,
        url: result.url,
        type: result.type,
        date: new Date().toISOString(),
        analyzed: false,
        findings: [],
      }));
 
      dispatch(addPreviousVisitLoadedImagesCompleteData(data));
      dispatch(addPreviousVisitLoadedXraysList(xrays));
      dispatch(togglePreviousVisitClicked());
 
      if (onImagesLoad) {
        onImagesLoad(xrays);
      }
 
      const updatedVisit = {
        ...latestVisit,
        xrays,
        imageCount: data.total,
      };
      onVisitSelect(updatedVisit);
     
    } catch (err) {
      console.error("Error during bulk upload:", err);
      throw err; // Re-throw to handle in calling component
    } finally {
      setLoadingImages((prev) => ({ ...prev, [visitId]: false }));
      dispatch(setIsBulkUploading(false));
    }
  };
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  }, []);

  const handleDrop = useCallback(
    async (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      const files = e.dataTransfer.files;
      if (!files || files.length === 0) return;
      await handleBulkUpload(files);
    },
    [handleBulkUpload, uploading, dispatch]
  );

  const fetchVisits = async () => {
    dispatch(setLoading(true));
    dispatch(setError(null));
    try {
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`,
        { method: "GET", credentials: "include" },
        router
      );
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to fetch visits");
      }
      const data = await response.json();
      const formattedVisits = (data.data ?? data).map((visit: any) => ({
        visitId: visit.visitId.toString(),
        visitDate: new Date(visit.visitDate).toLocaleDateString(),
        dentistName: displayName || visit.dentistName,
        createdAt: new Date(visit.createdAt).toLocaleString(),
        procedures: visit.procedures || [],
        imageCount: visit.imageCount || 0,
      }));
      dispatch(addPreviousPatientsVisits(formattedVisits));
    } catch (err: any) {
      dispatch(setError(err.message || "Unknown error"));
    } finally {
      dispatch(setLoading(false));
    }
  };

  useEffect(() => {
    if (patientId) {
      fetchVisits();
    }
  }, [patientId, router, dispatch]);


  const handleRemoveImage = async (slotId: string) => {
    try {
      const response = await fetchWithRefresh(
        REMOVE_UPLOADED_IMAGE,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({
            patientId,
            patientVisitId,
            imageType: slotId,
          }),
        },
        router
      );
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to delete image");
      }
      const { [slotId]: _, ...restSlots } = slots;
      dispatch(setSlots(restSlots));
      if (slotId === "PANORAMIC") {
        dispatch(setPanoramicImage(null));
      }
      if (previousVisitsImages) {
        dispatch(
          addPreviousVisitLoadedXraysList(
            previousVisitsImages.filter((xray) => xray.type !== slotId)
          )
        );
      }
      const { [slotId]: __, ...restAnnotations } = annotations;
      dispatch(setAnnotations(restAnnotations));
      await fetchVisits();
    } catch (error: any) {
      console.error("Error removing image:", error);
    }
  };


  // Count uploaded images from all sections: Intraoral (FMS_LAYOUT), Panoramic, and Miscellaneous
  const intraoralCount = FMS_LAYOUT.map((s) => s.id).filter((id) =>
    Boolean(slots[id])
  ).length;
  const uploadedCount = intraoralCount + panoramicCount + miscCount;

  const analyzedCount = Object.entries(slots)
    .filter(([slotId]) => slotId !== "PANORAMIC")
    .filter(([, img]) => img?.analyzed).length;
  const hasImages = uploadedCount > 0;

  // Check if there are any annotations available to display
  const hasDecayAnnotations = Object.values(annotations).some(
    (annotation) => annotation?.decay && annotation.decay.length > 0
  );

  const hasNumberingAnnotations = Object.values(annotations).some(
    (annotation) => annotation?.numbering && annotation.numbering.length > 0
  );

  const hasAnyAnnotations = hasDecayAnnotations || hasNumberingAnnotations;

  const canToggleDecay = analyzedCount > 0 && !globalAnalyzing && hasDecayAnnotations;
  const canToggleNumbering = analyzedCount > 0 && !globalAnalyzing && hasNumberingAnnotations;

  const miscImages = Object.entries(slots)
    .filter(
      ([slotKey, img]) =>
        !!img &&
        slotKey !== "PANORAMIC" &&
        !FMS_LAYOUT.some((s) => s.id === slotKey)
    )
    .map(([, img]) => img!) as XrayImage[];

  const isAnyUploading = Object.values(uploading).some(
    (isUploading) => isUploading
  );


  return (
    <div className="space-y-4">
      <div className="sticky top-0 z-10 bg-background/80 backdrop-blur-md border-b border-border py-3 px-4 rounded-lg shadow-sm">
        {/* make this whole row clickable */}
        <div
          className="flex items-center justify-between cursor-pointer select-none"
          onClick={() => dispatch(setIsCollapsed(!isCollapsed))}
        >
          {/* left side: chevron, title & badge */}
          <div className="flex items-center gap-2">
            {isCollapsed ? (
              <ChevronDown className="w-5 h-5 text-muted-foreground" />
            ) : (
              <ChevronUp className="w-5 h-5 text-muted-foreground" />
            )}
            <h3 className="text-lg text-foreground">Full Mouth Series</h3>
            {uploadedCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {uploadedCount}
              </Badge>
            )}
          </div>

          {/* right side: decay/numbering buttons */}
          {uploadedCount > 0 && (
            // stop the click from bubbling up
            <div
              className="flex items-center gap-4"
              onClick={(e) => e.stopPropagation()}
            >
              <button
               onClick={() => dispatch(toggleShowDecay())}

                disabled={!canToggleDecay}
                className={`px-3 py-1 rounded ${
               canToggleDecay
                    ? showDecay
                      ? "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200"
                      : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                    : "bg-muted text-muted-foreground cursor-not-allowed"
                }`}
              >
                {showDecay ? "Hide Decay" : "Show Decay"}
              </button>
              <button
            onClick={() => dispatch(toggleShowNumbering())}

                disabled={!canToggleNumbering}
                className={`px-3 py-1 rounded ${
               canToggleNumbering
                    ? showNumbering
                      ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200"
                      : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                    : "bg-muted text-muted-foreground cursor-not-allowed"
                }`}
              >
                {showNumbering ? "Hide Numbering" : "Show Numbering"}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Intraoral Section (always visible) */}
      {!isCollapsed && (
        <div
          className="relative max-w-7xl mx-auto"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {isUploading && (
         <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
                  <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
                  <div className="w-64 bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white mt-2">
                  {uploadStatus} ({uploadProgress}%)
                  </p>
                </div>
          )}
       {globalAnalyzing && (
  <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
    <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
    
    <div className="w-64 bg-gray-200 rounded-full h-2.5">
      <div
        className="bg-blue-600 h-2.5 rounded-full"
        style={{ width: `${analysisProgress}%` }}
      ></div>
    </div>

    <div className="text-white mt-2 text-sm">
      {analysisStatus} ({analysisProgress}%)
    </div>
  </div>
)}

          <div className="grid grid-cols-7 grid-rows-3 gap-4  rounded-lg">
            {FMS_LAYOUT.map((slot) => {
              const image = slots[slot.id];
              console.log("Slot Image:", image, "Slot ID:", slot.id);
              
              const isAnalyzing = analyzing[slot.id];
              const isUploading = false;
              const isEmpty = !image;
              const isAnalyzed = image?.analyzed || false;
              const slotHasDecay = isAnalyzed && hasDecay(slot.id);
              return (
                <div
                  key={slot.id}
                  className="relative aspect-square overflow-hidden"
                  style={{
                    gridColumn: slot.col + 1,
                    gridRow: slot.row + 1,
                  }}
                >
                  <Card
                    className={`group h-full cursor-pointer transition-all duration-200 overflow-hidden ${
                      isEmpty
                        ? "bg-muted-foreground/20 border-border hover:border-primary hover:bg-muted-foreground/30"
                        : "bg-card border-border hover:shadow-lg hover:border-primary"
                    }
                      ${
                        slotHasDecay
                          ? "ring-3 ring-red-500 border-4 border-red-500"
                          : isAnalyzed
                          ? "ring-3 ring-green-500 border-4 border-green-500"
                          : ""
                      }`}
                    onClick={() => image && handleCardClick(slot.id)}
                  >
                    <CardContent className="p-0 h-full flex flex-col">
                      {isEmpty ? (
                        <>
                          <div className="flex-1 flex items-center justify-center overflow-hidden">
                            {isUploading ? (
                              <div className=""></div>
                            ) : (
                              <div className="text-center">
                                <div className="text-[10px] sm:text-xs text-muted-foreground font-medium whitespace-pre-line leading-tight overflow-hidden">
                                  {slot.displayName}
                                </div>
                              </div>
                            )}
                          </div>
                          {!isUploading && (
                            <div className="mt-2 p-2">
                              <label className="block">
                                <input
                                  type="file"
                                  accept="image/*"
                                  className="hidden"
                                  onChange={(e) => {
                                    console.log("event e", e.target.files);
                                    handleFileUpload(
                                      slot.id,
                                      e.target.files,
                                      router
                                    );
                                  }}
                                />
<div className="w-full p-1 sm:p-2 bg-gray-400 rounded text-center cursor-pointer hover:bg-gray-500 transition-colors">
  <Upload className="w-3 h-3 sm:w-4 sm:h-4 mx-auto text-white" />
</div>
                              </label>
                            </div>
                          )}
                        </>
                      ) : (
                        <>
                          <div className="relative flex-1 overflow-hidden" style={{ isolation: "isolate" }}>
                            <img
                              src={image.url || "/placeholder.svg"}
                              alt={slot.displayName}
                              className="w-full h-full rounded max-w-full max-h-full"
                              ref={(el: HTMLImageElement | null) => {
                                if (el && (slotHasDecay || isAnalyzed)) {
                                  initializeCanvas(
                                    slot.id,
                                    canvasRefs.current[slot.id],
                                    el
                                  );
                                }
                              }}
                            />
                            {(slotHasDecay || isAnalyzed) && (
                              <canvas
                                ref={(el) =>
                                  initializeCanvas(
                                    slot.id,
                                    el,
                                    el?.previousSibling as HTMLImageElement
                                  )
                                }
                                style={{
                                  position: "absolute",
                                  top: 0,
                                  left: 0,
                                  width: "100%",
                                  height: "100%",
                                  pointerEvents: "none",
                                  zIndex: 1,
                                }}
                              />
                            )}
                            <button
                              className="absolute top-1 right-1 z-20 pointer-events-auto bg-red-600 hover:bg-red-700 text-white text-xl rounded-full w-7 h-7 flex items-center justify-center shadow-lg border-2 border-white leading-none p-0 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveImage(slot.id);
                              }}
                              title="Remove Image"
                            >
                              ×
                            </button>
                            {isAnalyzing && <div className=""></div>}
                          </div>
                          <div className="mt-2 flex gap-1 absolute bottom-4 left-2 right-2">
                            {!isAnalyzed && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleAnalyze(slot.id);
                                }}
                                className="flex-1 h-6 text-xs border-border text-foreground hover:bg-accent px-2"
                                disabled={isAnalyzing}
                              >
                                {isAnalyzing ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Analyzing...
                                  </>
                                ) : (
                                  "Analyze"
                                )}
                              </Button>
                            )}
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>
                </div>
              );
            })}
            <div
              className="col-span-3 row-start-2 col-start-3 relative"
              style={{ gridColumn: "3 / 6", gridRow: "2" }}
            >
              <label className="h-full block">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={(e) => handleBulkUpload(e.target.files)}
                />

    <div className="border-2 border-dashed border-border rounded-lg bg-card  flex items-center justify-center aspect-[3/1] cursor-pointer hover:bg-accent transition-colors">      
  <div className="text-center p-4">
    <Upload className="w-8 h-8 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
    <p className="text-sm text-blue-600 font-medium dark:text-blue-300">
      Drop images here or click to upload
    </p>
    <p className="text-xs text-blue-600 dark:text-blue-300">
      Supports multiple images
    </p>
  </div>
</div>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Panoramic Accordion */}
      <div className="bg-card rounded-lg shadow-sm border border-border">
        <button
          className="w-full flex items-center gap-2 p-4 text-left transition-colors"
          onClick={() => setPanoramicOpen(!panoramicOpen)}
          title={`${panoramicCount} image${panoramicCount === 1 ? "" : "s"}`}
        >
          {panoramicOpen ? (
            <ChevronUp className="w-5 h-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-5 h-5 text-muted-foreground" />
          )}
          <h3 className="font-medium text-foreground">Panoramic X-ray</h3>
          {panoramicCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {panoramicCount}
            </Badge>
          )}
        </button>

        {panoramicOpen && (
          <div className="p-4 pt-0">
    
            <div className="bg-muted  rounded-lg p-6">
              <div className="max-w-4xl mx-auto">
                {!slots?.PANORAMIC && (
                  <p className="text-sm text-muted-foreground mb-4">
                    Upload panoramic radiograph
                  </p>
                )}
                {slots?.PANORAMIC ? (
                  <div
                    className={`relative bg-card rounded-lg overflow-hidden shadow-sm cursor-pointer transition-all duration-200 ${
                      slots.PANORAMIC.analyzed && hasDecay("PANORAMIC")
                        ? "ring-3 ring-red-500 border-red-500"
                        : slots.PANORAMIC.analyzed
                        ? "ring-3 ring-green-500"
                        : "border-border hover:shadow-lg hover:border-primary"
                    }`}
                    onClick={() => handleCardClick("PANORAMIC")}
                  >
                    <div className="relative flex-1 overflow-hidden" style={{ isolation: "isolate" }}>
                      <img
                        src={slots.PANORAMIC.url || "/placeholder.svg"}
                        alt="Panoramic X-ray"
                        className="w-full h-auto max-h-[400px] object-contain rounded"
                        ref={(el: HTMLImageElement | null) => {
                          if (el)
                            initializeCanvas(
                              "PANORAMIC",
                              canvasRefs.current.PANORAMIC,
                              el
                            );
                        }}
                      />
                      <canvas
                        ref={(el) =>
                          initializeCanvas(
                            "PANORAMIC",
                            el,
                            el?.previousSibling as HTMLImageElement
                          )
                        }
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: "100%",
                          pointerEvents: "none",
                          zIndex: 1,
                        }}
                      />
                             {isUploading && (
              <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
                <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
                <div className="w-64 bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-white mt-2">
                {uploadStatus} ({uploadProgress}%)
                </p>
              </div>
            )}
            {globalAnalyzing && (
            <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
                  <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
                  <div className="w-64 bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${analysisProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white mt-2">
               {analysisStatus} ({analysisProgress}%)
                  </p>
                </div>
            )}
                      <button
                        className="absolute top-4 right-4 z-20 pointer-events-auto bg-red-600 hover:bg-red-700 text-white text-xl rounded-full w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white leading-none p-0 transition-opacity cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveImage("PANORAMIC");
                        }}
                        title="Remove Image"
                      >
                        ×
                      </button>
                    </div>
                    <div className="mt-2 flex gap-1 absolute bottom-4 left-2 right-2 justify-center">
                      {!slots.PANORAMIC.analyzed && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAnalyze("PANORAMIC");
                          }}
                          className="border-border text-foreground hover:bg-accent"
                          disabled={analyzing.PANORAMIC}
                        >
                          {analyzing.PANORAMIC ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Analyzing...
                            </>
                          ) : (
                            "Analyze"
                          )}
                        </Button>
                      )}
                      {slots.PANORAMIC.analyzed && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCardClick("PANORAMIC");
                          }}
                          className="border-green-500 text-green-600 hover:bg-green-50 dark:hover:bg-green-900 dark:text-green-200"
                        >
                          View <Eye className="w-3 h-3 mr-1" />
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <label className="block">
                    <input
                      id="panoramic-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) =>
                        handleFileUpload("PANORAMIC", e.target.files, router)
                      }
                    />
                    <div className="border-2 border-dashed border-border rounded-lg bg-card min-h-[300px] flex items-center justify-center cursor-pointer hover:bg-accent transition-colors">
                      <div className="text-center">
                        <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          Click to upload panoramic image
                        </p>
                      </div>
                    </div>
                  </label>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Miscellaneous Accordion */}

      <div className="bg-card rounded-lg shadow-sm border border-border">
        <button
          className="w-full flex items-center gap-2 p-4 text-left transition-colors"
          onClick={() => setMiscOpen(!miscOpen)}
          title={`${miscCount} image${miscCount === 1 ? "" : "s"}`}
        >
          {miscOpen ? (
            <ChevronUp className="w-5 h-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-5 h-5 text-muted-foreground" />
          )}
          <h3 className="font-medium text-foreground">Miscellaneous Images</h3>
          {miscCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {miscCount}
            </Badge>
          )}
        </button>

        {miscOpen && (
          <div className="p-4 pt-0">
            <div
              className="relative max-w-7xl mx-auto"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              {isUploading && (
                <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
                  <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
                  <div className="w-64 bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white mt-2">
                  {uploadStatus} ({uploadProgress}%)
                  </p>
                </div>
              )}
              {globalAnalyzing && (
                <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-20 rounded-lg">
                  <Loader2 className="w-12 h-12 text-white animate-spin mb-4" />
                  <div className="w-64 bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${analysisProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-white mt-2">
               {analysisStatus} ({analysisProgress}%)
                  </p>
                </div>
              )}

              {/* Scrollable container with max height */}
              <div className="max-h-[500px] overflow-y-auto">
                {/* Dynamic Grid */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 gap-4  rounded-lg p-4">
                  {/* Render all existing miscellaneous images */}
                  {Object.entries(slots)
                    .filter(
                      ([slotKey]) =>
                        slotKey !== "PANORAMIC" &&
                        !FMS_LAYOUT.some((s) => s.id === slotKey)
                    )
                    .map(([slotKey, img], index) => {
                      const isUploading = uploading[slotKey];
                      const isAnalyzing = analyzing[slotKey];
                      const isAnalyzed = img?.analyzed || false;
                      const slotHasDecay = isAnalyzed && hasDecay(slotKey);

                      return (
                        <div
                          key={slotKey}
                          className="relative aspect-square min-w-[120px]"
                        >
                          <Card
                            className={`group h-full cursor-pointer transition-all duration-200 ${
                              img
                                ? "bg-card border-border hover:shadow-lg hover:border-primary"
                                : "bg-muted-foreground/20 border-border hover:border-primary hover:bg-muted-foreground/30"
                            } ${
                              slotHasDecay
                                ? "ring-3 ring-red-500 border-4 border-red-500"
                                : isAnalyzed
                                ? "ring-3 ring-green-500 border-4 border-green-500"
                                : ""
                            }`}
                            onClick={() => img && handleCardClick(slotKey)}
                          >
                            <CardContent className="p-0 h-full flex flex-col">
                              <div className="relative aspect-square overflow-hidden" style={{ isolation: "isolate" }}>
                                {img  ? (
                                  <>
                                    <img
                                      src={img.url}
                                      alt={slotKey}
                                       className="w-full h-full rounded max-w-full max-h-full"
                                      ref={(el) => {
                                        if (el)
                                          initializeCanvas(
                                            slotKey,
                                            canvasRefs.current[slotKey],
                                            el
                                          );
                                      }}
                                    />
                                  {slotHasDecay && <canvas
                                      ref={(el) =>
                                        initializeCanvas(
                                          slotKey,
                                          el,
                                          el?.previousSibling as HTMLImageElement
                                        )
                                      }
                                      style={{
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        width: "100%",
                                        height: "100%",
                                        pointerEvents: "none",
                                        zIndex: 1,
                                      }}
                                    /> }
                                    
                                    <button
                                      className="absolute top-1 right-1 z-20 pointer-events-auto bg-red-600 hover:bg-red-700 text-white text-xl rounded-full w-7 h-7 flex items-center justify-center shadow-lg border-2 border-white leading-none p-0 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleRemoveImage(slotKey);
                                      }}
                                    >
                                      ×
                                    </button>
                                    {(isUploading ) && (
                                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded">
                                        <Loader2 className="w-6 h-6 text-white animate-spin" />
                                      </div>
                                    )}
                                  </>
                                ) : (
                                  <div className="flex flex-col justify-between h-full p-2">
                                    <span className="text-[10px] sm:text-xs text-muted-foreground pl-6 mt-8">
                                      Miscellaneous {index + 1}
                                    </span>
<label className="inline-flex items-center justify-center p-1 sm:p-2 bg-gray-400 rounded cursor-pointer hover:bg-gray-500 transition-colors">
  <input
    type="file"
    accept="image/*"
    className="hidden"
    onChange={(e) =>
      handleFileUpload(
        `MISC${index + 1}`,
        e.target.files,
        router
      )
    }
  />
  <Upload className="w-4 h-4 text-white" />
</label>
                                  </div>
                                )}
                              </div>
                              {img && (
                                <div className="mt-2 flex gap-1 absolute bottom-4 left-2 right-2">
                                  {!isAnalyzed && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAnalyze(slotKey);
                                      }}
                                      className="flex-1 h-6 text-xs border-border text-foreground hover:bg-accent px-2"
                                      disabled={isAnalyzing}
                                    >
                                      {isAnalyzing ? (
                                        <>
                                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                          Analyzing...
                                        </>
                                      ) : (
                                        "Analyze"
                                      )}
                                    </Button>
                                  )}
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        </div>
                      );
                    })}

                  {/* Always show at least 21 slots (7x3 grid) */}
                  {Array.from({
                    length: Math.max(
                      0,
                      21 -
                        Object.keys(slots).filter(
                          (key) =>
                            key !== "PANORAMIC" &&
                            !FMS_LAYOUT.some((s) => s.id === key)
                        ).length
                    ),
                  }).map((_, i) => {
                    const nextIndex =
                      Object.keys(slots).filter(
                        (key) =>
                          key !== "PANORAMIC" &&
                          !FMS_LAYOUT.some((s) => s.id === key)
                      ).length +
                      i +
                      1;

                    return (
                      <div
                        key={`empty-${i}`}
                        className="relative aspect-square min-w-[120px]"
                      >
                        <Card className="group h-full cursor-pointer transition-all duration-200 bg-muted-foreground/20 border-border hover:border-primary hover:bg-muted-foreground/30">
                          <CardContent className="p-0 h-full flex flex-col">
                            <div className="flex flex-col justify-between h-full p-2">
                              <span className="text-[10px] sm:text-xs text-muted-foreground pl-6 mt-8">
                                Miscellaneous {nextIndex}
                              </span>
<label className="inline-flex items-center justify-center p-1 sm:p-2 bg-gray-400 rounded cursor-pointer hover:bg-gray-500 transition-colors">
  <input
    type="file"
    accept="image/*"
    className="hidden"
    onChange={(e) =>
      handleFileUpload(
        `MISC${nextIndex}`,
        e.target.files,
        router
      )
    }
  />
  <Upload className="w-4 h-4 text-white" />
</label>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
